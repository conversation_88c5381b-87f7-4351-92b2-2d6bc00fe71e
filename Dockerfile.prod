# MCPHub 生产环境 Dockerfile
FROM node:18-alpine AS base

ENV npm_config_registry=https://registry.npmmirror.com

# 安装基础依赖
RUN apk add --no-cache \
    python3 \
    py3-pip \
    make \
    g++ \
    curl \
    git

# 安装 pnpm
RUN npm install -g pnpm

# 设置工作目录
WORKDIR /app

# 复制 package.json 和 pnpm-lock.yaml
COPY package.json pnpm-lock.yaml ./

# 安装依赖
RUN pnpm install --frozen-lockfile

# 构建阶段
FROM base AS builder

# 复制源代码
COPY . .

# 构建应用
RUN pnpm build

# 生产阶段
FROM node:18-alpine AS production

# 安装运行时依赖
RUN apk add --no-cache \
    python3 \
    py3-pip \
    curl \
    dumb-init

# 安装 pnpm
RUN npm install -g pnpm

# 创建应用用户
RUN addgroup -g 1001 -S nodejs && \
    adduser -S mcphub -u 1001 -G nodejs

# 设置工作目录
WORKDIR /app

# 复制 package.json
COPY package.json pnpm-lock.yaml ./

# 只安装生产依赖
RUN pnpm install --prod --frozen-lockfile && \
    pnpm store prune

# 复制构建文件
COPY --from=builder --chown=mcphub:nodejs /app/dist ./dist
COPY --from=builder --chown=mcphub:nodejs /app/frontend/dist ./frontend/dist
COPY --from=builder --chown=mcphub:nodejs /app/mcp_settings.json ./

# 创建日志目录
RUN mkdir -p logs && chown mcphub:nodejs logs

# 切换到应用用户
USER mcphub

# 暴露端口
EXPOSE 3000

# 设置环境变量
ENV NODE_ENV=production
ENV PORT=3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

# 使用 dumb-init 作为 PID 1
ENTRYPOINT ["dumb-init", "--"]

# 启动应用
CMD ["node", "dist/index.js"]
