#!/bin/bash

echo "=== MCPHub 网络连接测试 ==="
echo

# 获取服务器IP
SERVER_IP=$(ip route get ******* | awk '{print $7; exit}')
echo "服务器IP地址: $SERVER_IP"

# 测试本地连接
echo
echo "1. 测试本地连接..."
curl -s -o /dev/null -w "本地访问状态: %{http_code}\n" http://localhost:8080

# 测试IP连接
echo
echo "2. 测试IP连接..."
curl -s -o /dev/null -w "IP访问状态: %{http_code}\n" http://$SERVER_IP:8080

# 检查端口监听
echo
echo "3. 检查端口监听状态..."
ss -tlnp | grep :8080

# 检查Docker容器状态
echo
echo "4. 检查Docker容器状态..."
docker ps | grep mcphub

# 测试容器内部连接
echo
echo "5. 测试容器内部连接..."
docker exec mcphub-prod curl -s -o /dev/null -w "容器内部访问状态: %{http_code}\n" http://localhost:3000

echo
echo "=== 测试完成 ==="
echo "如果所有测试都显示200，说明服务器配置正确"
echo "如果外部无法访问，请检查："
echo "1. 客户端网络是否可以访问 $SERVER_IP"
echo "2. 客户端防火墙设置"
echo "3. 网络路由配置"
